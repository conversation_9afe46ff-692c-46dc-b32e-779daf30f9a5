import mongoose from 'mongoose'

const SessionSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  mentor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  scheduledDate: {
    type: Date,
    required: [true, 'Please provide a scheduled date'],
  },
  duration: {
    type: Number,
    default: 15, // minutes
    min: [15, 'Session duration must be at least 15 minutes'],
    max: [60, 'Session duration cannot exceed 60 minutes'],
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'completed', 'cancelled', 'no-show'],
    default: 'pending',
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending',
  },
  amount: {
    type: Number,
    required: [true, 'Please provide session amount'],
    min: [50, 'Session amount must be at least ₹50'],
  },
  paymentId: {
    type: String,
    default: '',
  },
  razorpayOrderId: {
    type: String,
    default: '',
  },
  razorpayPaymentId: {
    type: String,
    default: '',
  },
  razorpaySignature: {
    type: String,
    default: '',
  },
  studentQuestions: [{
    question: {
      type: String,
      maxlength: [500, 'Question cannot be more than 500 characters'],
    },
    priority: {
      type: Number,
      min: 1,
      max: 3,
      default: 1,
    },
  }],
  meetingLink: {
    type: String,
    default: '',
  },
  meetingId: {
    type: String,
    default: '',
  },
  notes: {
    student: {
      type: String,
      maxlength: [1000, 'Student notes cannot be more than 1000 characters'],
    },
    mentor: {
      type: String,
      maxlength: [1000, 'Mentor notes cannot be more than 1000 characters'],
    },
  },
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5,
    },
    comment: {
      type: String,
      maxlength: [500, 'Feedback comment cannot be more than 500 characters'],
    },
    isPublic: {
      type: Boolean,
      default: true,
    },
  },
  reminderSent: {
    type: Boolean,
    default: false,
  },
  completedAt: {
    type: Date,
  },
  cancelledAt: {
    type: Date,
  },
  cancellationReason: {
    type: String,
    maxlength: [200, 'Cancellation reason cannot be more than 200 characters'],
  },
  cancelledBy: {
    type: String,
    enum: ['student', 'mentor', 'admin'],
  },
}, {
  timestamps: true,
})

// Indexes for better query performance
SessionSchema.index({ student: 1, createdAt: -1 })
SessionSchema.index({ mentor: 1, createdAt: -1 })
SessionSchema.index({ scheduledDate: 1 })
SessionSchema.index({ status: 1, scheduledDate: 1 })
SessionSchema.index({ paymentStatus: 1 })

export default mongoose.models.Session || mongoose.model('Session', SessionSchema)
