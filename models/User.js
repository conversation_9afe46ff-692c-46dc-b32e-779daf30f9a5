import mongoose from 'mongoose'

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a name'],
    maxlength: [60, 'Name cannot be more than 60 characters'],
  },
  email: {
    type: String,
    required: [true, 'Please provide an email'],
    unique: true,
    lowercase: true,
  },
  image: {
    type: String,
    default: '',
  },
  role: {
    type: String,
    enum: ['student', 'mentor', 'admin'],
    default: 'student',
  },
  // Mentor-specific fields
  bio: {
    type: String,
    maxlength: [500, 'Bio cannot be more than 500 characters'],
  },
  batch: {
    type: String,
    maxlength: [20, 'Batch cannot be more than 20 characters'],
  },
  field: {
    type: String,
    maxlength: [100, 'Field cannot be more than 100 characters'],
  },
  interests: [{
    type: String,
    maxlength: [50, 'Interest cannot be more than 50 characters'],
  }],
  languages: [{
    type: String,
    maxlength: [30, 'Language cannot be more than 30 characters'],
  }],
  hourlyRate: {
    type: Number,
    default: 99,
    min: [50, 'Hourly rate must be at least ₹50'],
    max: [500, 'Hourly rate cannot exceed ₹500'],
  },
  availability: [{
    day: {
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
    },
    slots: [{
      startTime: String,
      endTime: String,
      isAvailable: {
        type: Boolean,
        default: true,
      },
    }],
  }],
  totalEarnings: {
    type: Number,
    default: 0,
  },
  totalSessions: {
    type: Number,
    default: 0,
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },
    count: {
      type: Number,
      default: 0,
    },
  },
  isApproved: {
    type: Boolean,
    default: false,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  phone: {
    type: String,
    maxlength: [15, 'Phone number cannot be more than 15 characters'],
  },
  college: {
    type: String,
    maxlength: [100, 'College name cannot be more than 100 characters'],
  },
  graduationYear: {
    type: Number,
    min: [2020, 'Graduation year must be 2020 or later'],
    max: [2030, 'Graduation year cannot exceed 2030'],
  },
}, {
  timestamps: true,
})

// Index for better query performance
UserSchema.index({ role: 1, isApproved: 1, isActive: 1 })
UserSchema.index({ 'rating.average': -1 })
UserSchema.index({ email: 1 })

export default mongoose.models.User || mongoose.model('User', UserSchema)
