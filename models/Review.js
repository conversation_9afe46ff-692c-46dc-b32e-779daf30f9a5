import mongoose from 'mongoose'

const ReviewSchema = new mongoose.Schema({
  session: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Session',
    required: true,
    unique: true,
  },
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  mentor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  rating: {
    type: Number,
    required: [true, 'Please provide a rating'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5'],
  },
  comment: {
    type: String,
    required: [true, 'Please provide a comment'],
    maxlength: [500, 'Comment cannot be more than 500 characters'],
    minlength: [10, 'Comment must be at least 10 characters'],
  },
  isPublic: {
    type: Boolean,
    default: true,
  },
  isVerified: {
    type: Boolean,
    default: true, // Since it's tied to a completed session
  },
  helpfulVotes: {
    type: Number,
    default: 0,
  },
  reportedBy: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    reason: {
      type: String,
      enum: ['inappropriate', 'spam', 'fake', 'other'],
    },
    reportedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  isHidden: {
    type: Boolean,
    default: false,
  },
  tags: [{
    type: String,
    enum: [
      'helpful',
      'knowledgeable',
      'patient',
      'clear-communication',
      'punctual',
      'motivating',
      'practical-advice',
      'career-guidance',
      'academic-help',
      'friendly'
    ],
  }],
}, {
  timestamps: true,
})

// Indexes for better query performance
ReviewSchema.index({ mentor: 1, isPublic: 1, isHidden: 1 })
ReviewSchema.index({ student: 1 })
ReviewSchema.index({ rating: -1 })
ReviewSchema.index({ createdAt: -1 })

export default mongoose.models.Review || mongoose.model('Review', ReviewSchema)
