import { useState, useEffect } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import Layout from '../../components/Layout'
import { Search, Filter, Star, Clock, Users } from 'lucide-react'

export default function MentorsPage() {
  const [mentors, setMentors] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    field: '',
    batch: '',
    language: '',
    minRating: 0,
    sortBy: 'rating'
  })
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    fetchMentors()
  }, [filters])

  const fetchMentors = async () => {
    setLoading(true)
    try {
      const queryParams = new URLSearchParams({
        ...filters,
        search: searchTerm
      }).toString()
      
      const response = await fetch(`/api/mentors?${queryParams}`)
      if (response.ok) {
        const data = await response.json()
        setMentors(data.mentors || [])
      }
    } catch (error) {
      console.error('Error fetching mentors:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    fetchMentors()
  }

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const clearFilters = () => {
    setFilters({
      field: '',
      batch: '',
      language: '',
      minRating: 0,
      sortBy: 'rating'
    })
    setSearchTerm('')
  }

  return (
    <Layout>
      <Head>
        <title>Find Mentors - NSTalk</title>
        <meta name="description" content="Browse and connect with NST student mentors. Find the perfect mentor for your needs." />
      </Head>

      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="font-display font-bold text-3xl md:text-4xl text-primary-900 mb-4">
              Find Your Perfect Mentor
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl">
              Connect with current NST students who can guide you through your journey
            </p>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="font-semibold text-lg text-primary-900">Filters</h2>
                  <button
                    onClick={clearFilters}
                    className="text-sm text-accent-600 hover:text-accent-700"
                  >
                    Clear All
                  </button>
                </div>

                {/* Search */}
                <form onSubmit={handleSearch} className="mb-6">
                  <label className="label">Search</label>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search by name, field, or interests..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="input-field pr-10"
                    />
                    <button
                      type="submit"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <Search className="w-4 h-4" />
                    </button>
                  </div>
                </form>

                {/* Field Filter */}
                <div className="mb-6">
                  <label className="label">Field of Study</label>
                  <select
                    value={filters.field}
                    onChange={(e) => handleFilterChange('field', e.target.value)}
                    className="input-field"
                  >
                    <option value="">All Fields</option>
                    <option value="Computer Science">Computer Science</option>
                    <option value="Engineering">Engineering</option>
                    <option value="Business">Business</option>
                    <option value="Medicine">Medicine</option>
                    <option value="Arts">Arts</option>
                    <option value="Science">Science</option>
                  </select>
                </div>

                {/* Batch Filter */}
                <div className="mb-6">
                  <label className="label">Batch</label>
                  <select
                    value={filters.batch}
                    onChange={(e) => handleFilterChange('batch', e.target.value)}
                    className="input-field"
                  >
                    <option value="">All Batches</option>
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                  </select>
                </div>

                {/* Language Filter */}
                <div className="mb-6">
                  <label className="label">Language</label>
                  <select
                    value={filters.language}
                    onChange={(e) => handleFilterChange('language', e.target.value)}
                    className="input-field"
                  >
                    <option value="">All Languages</option>
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Bengali">Bengali</option>
                    <option value="Tamil">Tamil</option>
                    <option value="Telugu">Telugu</option>
                  </select>
                </div>

                {/* Rating Filter */}
                <div className="mb-6">
                  <label className="label">Minimum Rating</label>
                  <select
                    value={filters.minRating}
                    onChange={(e) => handleFilterChange('minRating', Number(e.target.value))}
                    className="input-field"
                  >
                    <option value={0}>Any Rating</option>
                    <option value={4}>4+ Stars</option>
                    <option value={4.5}>4.5+ Stars</option>
                  </select>
                </div>

                {/* Sort By */}
                <div className="mb-6">
                  <label className="label">Sort By</label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                    className="input-field"
                  >
                    <option value="rating">Highest Rated</option>
                    <option value="sessions">Most Sessions</option>
                    <option value="newest">Newest First</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Mentors Grid */}
            <div className="lg:w-3/4">
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, index) => (
                    <div key={index} className="bg-white rounded-lg shadow-sm p-6 animate-pulse">
                      <div className="flex items-center mb-4">
                        <div className="w-16 h-16 bg-gray-200 rounded-full mr-4"></div>
                        <div>
                          <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-16"></div>
                        </div>
                      </div>
                      <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4 mb-4"></div>
                      <div className="h-8 bg-gray-200 rounded w-full"></div>
                    </div>
                  ))}
                </div>
              ) : mentors.length > 0 ? (
                <>
                  <div className="flex items-center justify-between mb-6">
                    <p className="text-gray-600">
                      Found {mentors.length} mentor{mentors.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {mentors.map((mentor) => (
                      <div key={mentor._id} className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-200">
                        <div className="flex items-center mb-4">
                          <img
                            src={mentor.image || '/default-avatar.png'}
                            alt={mentor.name}
                            className="w-16 h-16 rounded-full mr-4"
                          />
                          <div>
                            <h3 className="font-semibold text-lg text-primary-900">{mentor.name}</h3>
                            <p className="text-gray-600">{mentor.field}</p>
                            <div className="flex items-center mt-1">
                              <Star className="w-4 h-4 text-yellow-400 fill-current" />
                              <span className="text-sm text-gray-600 ml-1">
                                {mentor.rating.average > 0 ? mentor.rating.average.toFixed(1) : 'New'}
                                {mentor.rating.count > 0 && ` (${mentor.rating.count})`}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-gray-600 mb-4 line-clamp-3">{mentor.bio}</p>
                        
                        <div className="flex flex-wrap gap-2 mb-4">
                          {mentor.interests.slice(0, 3).map((interest, index) => (
                            <span key={index} className="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-full">
                              {interest}
                            </span>
                          ))}
                        </div>
                        
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center text-sm text-gray-600">
                            <Clock className="w-4 h-4 mr-1" />
                            <span>15 min</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Users className="w-4 h-4 mr-1" />
                            <span>{mentor.totalSessions} sessions</span>
                          </div>
                        </div>
                        
                        <Link
                          href={`/mentors/${mentor._id}`}
                          className="btn-primary w-full text-center"
                        >
                          Book Session - ₹{mentor.hourlyRate}
                        </Link>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-12 h-12 text-gray-400" />
                  </div>
                  <h3 className="font-semibold text-lg text-gray-900 mb-2">No mentors found</h3>
                  <p className="text-gray-600 mb-4">Try adjusting your filters or search terms</p>
                  <button
                    onClick={clearFilters}
                    className="btn-primary"
                  >
                    Clear Filters
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
