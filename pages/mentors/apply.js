import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Layout from '../../components/Layout'
import { CheckCircle, Users, DollarSign, Clock } from 'lucide-react'
import toast from 'react-hot-toast'

export default function ApplyMentor() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    bio: '',
    field: '',
    batch: '',
    interests: '',
    languages: '',
    phone: '',
    college: 'NST', // Default to NST
    graduationYear: new Date().getFullYear(),
    hourlyRate: 99,
  })

  const benefits = [
    {
      icon: DollarSign,
      title: 'Earn Money',
      description: 'Get paid ₹99 for every 15-minute session you conduct'
    },
    {
      icon: Users,
      title: 'Help Students',
      description: 'Share your experience and guide aspiring students'
    },
    {
      icon: Clock,
      title: 'Flexible Schedule',
      description: 'Set your own availability and work on your terms'
    },
    {
      icon: CheckCircle,
      title: 'Build Network',
      description: 'Connect with students and build your professional network'
    }
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (status !== 'authenticated') {
      toast.error('Please sign in to apply as a mentor')
      router.push('/auth/signin?callbackUrl=/mentors/apply')
      return
    }

    setLoading(true)
    
    try {
      // Convert interests and languages to arrays
      const interests = formData.interests.split(',').map(item => item.trim()).filter(Boolean)
      const languages = formData.languages.split(',').map(item => item.trim()).filter(Boolean)
      
      const response = await fetch('/api/mentors/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          interests,
          languages,
          hourlyRate: Number(formData.hourlyRate),
          graduationYear: Number(formData.graduationYear),
        }),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success('Application submitted successfully! We\'ll review it soon.')
        router.push('/dashboard/mentor')
      } else {
        toast.error(data.message || 'Failed to submit application')
      }
    } catch (error) {
      console.error('Error submitting application:', error)
      toast.error('Something went wrong. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading') {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-500"></div>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <Head>
        <title>Become a Mentor - NSTalk</title>
        <meta name="description" content="Apply to become a mentor on NSTalk and help aspiring students while earning money." />
      </Head>

      <div className="bg-gray-50 min-h-screen">
        {/* Hero Section */}
        <div className="hero-gradient text-white py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 className="font-display font-bold text-4xl md:text-5xl mb-6">
              Become a <span className="text-accent-400">Mentor</span>
            </h1>
            <p className="text-xl text-primary-200 mb-8 max-w-2xl mx-auto">
              Share your NST experience, help aspiring students, and earn money doing what you love
            </p>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="py-16 bg-white">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="font-display font-bold text-3xl text-center text-primary-900 mb-12">
              Why Become a Mentor?
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-8 h-8 text-accent-600" />
                  </div>
                  <h3 className="font-semibold text-lg text-primary-900 mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600">
                    {benefit.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Application Form */}
        <div className="py-16">
          <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="font-display font-bold text-2xl text-primary-900 mb-6">
                Mentor Application
              </h2>
              
              {!session ? (
                <div className="text-center py-8">
                  <p className="text-gray-600 mb-4">Please sign in to apply as a mentor</p>
                  <button
                    onClick={() => router.push('/auth/signin?callbackUrl=/mentors/apply')}
                    className="btn-primary"
                  >
                    Sign In to Continue
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label className="label">Bio *</label>
                    <textarea
                      name="bio"
                      value={formData.bio}
                      onChange={handleInputChange}
                      required
                      rows={4}
                      placeholder="Tell us about yourself, your experience at NST, and what you can help students with..."
                      className="input-field resize-none"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="label">Field of Study *</label>
                      <input
                        type="text"
                        name="field"
                        value={formData.field}
                        onChange={handleInputChange}
                        required
                        placeholder="e.g., Computer Science"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="label">Batch *</label>
                      <select
                        name="batch"
                        value={formData.batch}
                        onChange={handleInputChange}
                        required
                        className="input-field"
                      >
                        <option value="">Select Batch</option>
                        <option value="2024">2024</option>
                        <option value="2023">2023</option>
                        <option value="2022">2022</option>
                        <option value="2021">2021</option>
                        <option value="2020">2020</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="label">Areas of Interest *</label>
                    <input
                      type="text"
                      name="interests"
                      value={formData.interests}
                      onChange={handleInputChange}
                      required
                      placeholder="e.g., Web Development, Data Science, Career Guidance (comma-separated)"
                      className="input-field"
                    />
                    <p className="text-sm text-gray-500 mt-1">Separate multiple interests with commas</p>
                  </div>

                  <div>
                    <label className="label">Languages *</label>
                    <input
                      type="text"
                      name="languages"
                      value={formData.languages}
                      onChange={handleInputChange}
                      required
                      placeholder="e.g., English, Hindi, Bengali (comma-separated)"
                      className="input-field"
                    />
                    <p className="text-sm text-gray-500 mt-1">Languages you can conduct sessions in</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="label">Phone Number *</label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        placeholder="+91 9876543210"
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="label">Graduation Year *</label>
                      <input
                        type="number"
                        name="graduationYear"
                        value={formData.graduationYear}
                        onChange={handleInputChange}
                        required
                        min="2020"
                        max="2030"
                        className="input-field"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="label">Session Rate (₹)</label>
                    <input
                      type="number"
                      name="hourlyRate"
                      value={formData.hourlyRate}
                      onChange={handleInputChange}
                      min="50"
                      max="500"
                      className="input-field"
                    />
                    <p className="text-sm text-gray-500 mt-1">Default is ₹99 for 15-minute sessions</p>
                  </div>

                  <div className="bg-accent-50 rounded-lg p-4">
                    <h3 className="font-semibold text-accent-900 mb-2">Application Process</h3>
                    <ul className="text-sm text-accent-700 space-y-1">
                      <li>• We'll review your application within 2-3 business days</li>
                      <li>• You may be contacted for a brief verification call</li>
                      <li>• Once approved, you can start accepting bookings</li>
                      <li>• Payments are processed weekly to your bank account</li>
                    </ul>
                  </div>

                  <button
                    type="submit"
                    disabled={loading}
                    className="btn-primary w-full py-3 text-lg"
                  >
                    {loading ? 'Submitting Application...' : 'Submit Application'}
                  </button>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
