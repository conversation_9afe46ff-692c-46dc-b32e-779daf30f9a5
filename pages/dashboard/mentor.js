import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Layout from '../../components/Layout'
import { Calendar, DollarSign, Users, Clock, CheckCircle, AlertCircle } from 'lucide-react'

export default function MentorDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [dashboardData, setDashboardData] = useState({
    stats: {
      totalEarnings: 0,
      totalSessions: 0,
      upcomingSessions: 0,
      rating: 0,
    },
    recentSessions: [],
    upcomingSessions: [],
  })

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin?callbackUrl=/dashboard/mentor')
      return
    }

    if (session?.user) {
      fetchDashboardData()
    }
  }, [session, status, router])

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/dashboard/mentor')
      if (response.ok) {
        const data = await response.json()
        setDashboardData(data)
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (status === 'loading' || loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-accent-500"></div>
        </div>
      </Layout>
    )
  }

  if (!session) {
    return null
  }

  // Check if user is a mentor
  if (session.user.role !== 'mentor') {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600 mb-6">You need to be a mentor to access this page.</p>
            <button
              onClick={() => router.push('/mentors/apply')}
              className="btn-primary"
            >
              Apply to be a Mentor
            </button>
          </div>
        </div>
      </Layout>
    )
  }

  const stats = [
    {
      name: 'Total Earnings',
      value: `₹${dashboardData.stats.totalEarnings.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-600',
      bg: 'bg-green-100',
    },
    {
      name: 'Total Sessions',
      value: dashboardData.stats.totalSessions,
      icon: Users,
      color: 'text-blue-600',
      bg: 'bg-blue-100',
    },
    {
      name: 'Upcoming Sessions',
      value: dashboardData.stats.upcomingSessions,
      icon: Calendar,
      color: 'text-purple-600',
      bg: 'bg-purple-100',
    },
    {
      name: 'Average Rating',
      value: dashboardData.stats.rating > 0 ? dashboardData.stats.rating.toFixed(1) : 'New',
      icon: CheckCircle,
      color: 'text-yellow-600',
      bg: 'bg-yellow-100',
    },
  ]

  return (
    <Layout>
      <Head>
        <title>Mentor Dashboard - NSTalk</title>
        <meta name="description" content="Manage your mentoring sessions and track your earnings" />
      </Head>

      <div className="bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="font-display font-bold text-3xl text-primary-900 mb-2">
              Welcome back, {session.user.name}!
            </h1>
            <p className="text-gray-600">
              Here's an overview of your mentoring activity
            </p>
          </div>

          {/* Approval Status */}
          {!session.user.isApproved && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-yellow-600 mr-3" />
                <div>
                  <h3 className="font-semibold text-yellow-800">Application Under Review</h3>
                  <p className="text-yellow-700 text-sm">
                    Your mentor application is being reviewed. You'll be able to accept bookings once approved.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat) => (
              <div key={stat.name} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center">
                  <div className={`p-3 rounded-lg ${stat.bg} mr-4`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Upcoming Sessions */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h2 className="font-semibold text-lg text-gray-900">Upcoming Sessions</h2>
              </div>
              <div className="p-6">
                {dashboardData.upcomingSessions.length > 0 ? (
                  <div className="space-y-4">
                    {dashboardData.upcomingSessions.map((session) => (
                      <div key={session._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{session.student.name}</p>
                          <p className="text-sm text-gray-600">
                            {new Date(session.scheduledDate).toLocaleDateString()} at{' '}
                            {new Date(session.scheduledDate).toLocaleTimeString([], { 
                              hour: '2-digit', 
                              minute: '2-digit' 
                            })}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-green-600">₹{session.amount}</p>
                          <p className="text-sm text-gray-600">{session.duration} min</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No upcoming sessions</p>
                  </div>
                )}
              </div>
            </div>

            {/* Recent Sessions */}
            <div className="bg-white rounded-lg shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <h2 className="font-semibold text-lg text-gray-900">Recent Sessions</h2>
              </div>
              <div className="p-6">
                {dashboardData.recentSessions.length > 0 ? (
                  <div className="space-y-4">
                    {dashboardData.recentSessions.map((session) => (
                      <div key={session._id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{session.student.name}</p>
                          <p className="text-sm text-gray-600">
                            {new Date(session.completedAt || session.scheduledDate).toLocaleDateString()}
                          </p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            session.status === 'completed' 
                              ? 'bg-green-100 text-green-800'
                              : session.status === 'cancelled'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {session.status}
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-green-600">₹{session.amount}</p>
                          {session.feedback?.rating && (
                            <div className="flex items-center text-sm text-gray-600">
                              <span className="text-yellow-400">★</span>
                              <span className="ml-1">{session.feedback.rating}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No recent sessions</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
            <h2 className="font-semibold text-lg text-gray-900 mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => router.push('/dashboard/mentor/availability')}
                className="btn-primary text-center"
              >
                Manage Availability
              </button>
              <button
                onClick={() => router.push('/dashboard/mentor/profile')}
                className="btn-secondary text-center"
              >
                Edit Profile
              </button>
              <button
                onClick={() => router.push('/dashboard/mentor/earnings')}
                className="btn-outline text-center"
              >
                View Earnings
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  )
}
