import NextAuth from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import dbConnect from '../../../lib/mongodb'
import User from '../../../models/User'

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
  ],
  callbacks: {
    async session({ session, token, user }) {
      // Connect to database
      await dbConnect()
      
      // Get user from database with additional fields
      const dbUser = await User.findOne({ email: session.user.email })
      
      if (dbUser) {
        session.user.id = dbUser._id.toString()
        session.user.role = dbUser.role
        session.user.isApproved = dbUser.isApproved
        session.user.isActive = dbUser.isActive
      }
      
      return session
    },
    async signIn({ user, account, profile }) {
      await dbConnect()
      
      // Check if user exists, if not create one
      const existingUser = await User.findOne({ email: user.email })
      
      if (!existingUser) {
        await User.create({
          name: user.name,
          email: user.email,
          image: user.image,
          role: 'student', // Default role
        })
      }
      
      return true
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
  },
  secret: process.env.NEXTAUTH_SECRET,
}

export default NextAuth(authOptions)
