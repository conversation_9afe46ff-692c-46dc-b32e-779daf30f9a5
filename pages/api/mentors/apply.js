import { getServerSession } from 'next-auth/next'
import { authOptions } from '../auth/[...nextauth]'
import dbConnect from '../../../lib/mongodb'
import User from '../../../models/User'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const session = await getServerSession(req, res, authOptions)
    
    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' })
    }

    await dbConnect()

    const {
      bio,
      field,
      batch,
      interests,
      languages,
      phone,
      college = 'NST',
      graduationYear,
      hourlyRate = 99,
    } = req.body

    // Validate required fields
    if (!bio || !field || !batch || !interests || !languages || !phone || !graduationYear) {
      return res.status(400).json({ 
        message: 'Please fill in all required fields' 
      })
    }

    // Validate arrays
    if (!Array.isArray(interests) || !Array.isArray(languages)) {
      return res.status(400).json({ 
        message: 'Interests and languages must be arrays' 
      })
    }

    // Validate rate
    if (hourlyRate < 50 || hourlyRate > 500) {
      return res.status(400).json({ 
        message: 'Session rate must be between ₹50 and ₹500' 
      })
    }

    // Check if user already exists and update their profile
    const existingUser = await User.findOne({ email: session.user.email })
    
    if (!existingUser) {
      return res.status(404).json({ message: 'User not found' })
    }

    // Check if user is already a mentor
    if (existingUser.role === 'mentor') {
      return res.status(400).json({ 
        message: 'You have already applied to be a mentor' 
      })
    }

    // Update user to mentor with application data
    const updatedUser = await User.findByIdAndUpdate(
      existingUser._id,
      {
        role: 'mentor',
        bio,
        field,
        batch,
        interests,
        languages,
        phone,
        college,
        graduationYear,
        hourlyRate,
        isApproved: false, // Needs admin approval
        isActive: true,
      },
      { new: true }
    )

    res.status(200).json({
      success: true,
      message: 'Mentor application submitted successfully',
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        isApproved: updatedUser.isApproved,
      },
    })
  } catch (error) {
    console.error('Error submitting mentor application:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
}
