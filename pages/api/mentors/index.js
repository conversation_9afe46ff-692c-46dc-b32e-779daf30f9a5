import dbConnect from '../../../lib/mongodb'
import User from '../../../models/User'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    await dbConnect()

    const {
      search = '',
      field = '',
      batch = '',
      language = '',
      minRating = 0,
      sortBy = 'rating',
      page = 1,
      limit = 12
    } = req.query

    // Build query
    let query = {
      role: 'mentor',
      isApproved: true,
      isActive: true,
    }

    // Add search filter
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { field: { $regex: search, $options: 'i' } },
        { bio: { $regex: search, $options: 'i' } },
        { interests: { $in: [new RegExp(search, 'i')] } },
      ]
    }

    // Add field filter
    if (field) {
      query.field = { $regex: field, $options: 'i' }
    }

    // Add batch filter
    if (batch) {
      query.batch = batch
    }

    // Add language filter
    if (language) {
      query.languages = { $in: [language] }
    }

    // Add rating filter
    if (minRating > 0) {
      query['rating.average'] = { $gte: parseFloat(minRating) }
    }

    // Build sort options
    let sortOptions = {}
    switch (sortBy) {
      case 'rating':
        sortOptions = { 'rating.average': -1, 'rating.count': -1 }
        break
      case 'sessions':
        sortOptions = { totalSessions: -1 }
        break
      case 'newest':
        sortOptions = { createdAt: -1 }
        break
      case 'price-low':
        sortOptions = { hourlyRate: 1 }
        break
      case 'price-high':
        sortOptions = { hourlyRate: -1 }
        break
      default:
        sortOptions = { 'rating.average': -1, 'rating.count': -1 }
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit)

    // Execute query
    const mentors = await User.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .select('name email image bio field interests languages hourlyRate rating totalSessions batch createdAt')

    // Get total count for pagination
    const totalCount = await User.countDocuments(query)
    const totalPages = Math.ceil(totalCount / parseInt(limit))

    res.status(200).json({
      success: true,
      mentors,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1,
      },
    })
  } catch (error) {
    console.error('Error fetching mentors:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
}
