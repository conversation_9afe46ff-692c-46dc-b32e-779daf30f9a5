import dbConnect from '../../../lib/mongodb'
import User from '../../../models/User'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    await dbConnect()

    // Get top-rated mentors who are approved and active
    const mentors = await User.find({
      role: 'mentor',
      isApproved: true,
      isActive: true,
    })
    .sort({ 'rating.average': -1, 'rating.count': -1 })
    .limit(6)
    .select('name email image bio field interests languages hourlyRate rating totalSessions batch')

    res.status(200).json({
      success: true,
      mentors,
    })
  } catch (error) {
    console.error('Error fetching featured mentors:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
}
