import { getServerSession } from 'next-auth/next'
import { authOptions } from '../auth/[...nextauth]'
import dbConnect from '../../../lib/mongodb'
import User from '../../../models/User'
import Session from '../../../models/Session'

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const session = await getServerSession(req, res, authOptions)
    
    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' })
    }

    await dbConnect()

    // Get mentor user data
    const mentor = await User.findOne({ email: session.user.email })
    
    if (!mentor || mentor.role !== 'mentor') {
      return res.status(403).json({ message: 'Access denied. Mentor role required.' })
    }

    // Get upcoming sessions
    const upcomingSessions = await Session.find({
      mentor: mentor._id,
      scheduledDate: { $gte: new Date() },
      status: { $in: ['pending', 'confirmed'] },
    })
    .populate('student', 'name email image')
    .sort({ scheduledDate: 1 })
    .limit(5)

    // Get recent sessions
    const recentSessions = await Session.find({
      mentor: mentor._id,
      status: { $in: ['completed', 'cancelled'] },
    })
    .populate('student', 'name email image')
    .sort({ updatedAt: -1 })
    .limit(5)

    // Calculate stats
    const totalSessions = await Session.countDocuments({
      mentor: mentor._id,
      status: 'completed',
    })

    const completedSessions = await Session.find({
      mentor: mentor._id,
      status: 'completed',
    })

    const totalEarnings = completedSessions.reduce((sum, session) => sum + session.amount, 0)

    const upcomingCount = await Session.countDocuments({
      mentor: mentor._id,
      scheduledDate: { $gte: new Date() },
      status: { $in: ['pending', 'confirmed'] },
    })

    res.status(200).json({
      success: true,
      stats: {
        totalEarnings,
        totalSessions,
        upcomingSessions: upcomingCount,
        rating: mentor.rating.average,
      },
      upcomingSessions,
      recentSessions,
    })
  } catch (error) {
    console.error('Error fetching mentor dashboard data:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    })
  }
}
