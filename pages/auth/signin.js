import { useState, useEffect } from 'react'
import { getSession, getProviders, signIn } from 'next-auth/react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { Chrome, ArrowLeft } from 'lucide-react'

export default function SignIn({ providers }) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const { callbackUrl = '/' } = router.query

  useEffect(() => {
    // Check if user is already signed in
    getSession().then((session) => {
      if (session) {
        router.push(callbackUrl)
      }
    })
  }, [callbackUrl, router])

  const handleSignIn = async (providerId) => {
    setLoading(true)
    try {
      await signIn(providerId, { callbackUrl })
    } catch (error) {
      console.error('Sign in error:', error)
      setLoading(false)
    }
  }

  return (
    <>
      <Head>
        <title>Sign In - NSTalk</title>
        <meta name="description" content="Sign in to NSTalk to connect with NST mentors" />
      </Head>

      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          {/* Back to Home */}
          <div className="mb-6">
            <Link
              href="/"
              className="flex items-center text-gray-600 hover:text-primary-900 transition-colors duration-200"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Link>
          </div>

          {/* Logo */}
          <div className="flex justify-center mb-6">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-12 h-12 bg-accent-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">NS</span>
              </div>
              <span className="font-display font-bold text-2xl text-primary-900">
                NSTalk
              </span>
            </Link>
          </div>

          <h2 className="text-center text-3xl font-display font-bold text-primary-900 mb-2">
            Welcome back
          </h2>
          <p className="text-center text-gray-600 mb-8">
            Sign in to your account to continue
          </p>
        </div>

        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
            <div className="space-y-4">
              {providers &&
                Object.values(providers).map((provider) => (
                  <div key={provider.name}>
                    <button
                      onClick={() => handleSignIn(provider.id)}
                      disabled={loading}
                      className="w-full flex justify-center items-center px-4 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                    >
                      {loading ? (
                        <div className="w-5 h-5 border-2 border-gray-300 border-t-accent-500 rounded-full animate-spin mr-3"></div>
                      ) : (
                        <Chrome className="w-5 h-5 mr-3 text-red-500" />
                      )}
                      {loading ? 'Signing in...' : `Continue with ${provider.name}`}
                    </button>
                  </div>
                ))}
            </div>

            <div className="mt-8">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">
                    New to NSTalk?
                  </span>
                </div>
              </div>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-600">
                  By signing in, you agree to our{' '}
                  <Link href="/terms" className="text-accent-600 hover:text-accent-500">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-accent-600 hover:text-accent-500">
                    Privacy Policy
                  </Link>
                </p>
              </div>
            </div>
          </div>

          <div className="mt-8 text-center">
            <div className="bg-accent-50 rounded-lg p-4">
              <h3 className="font-semibold text-accent-900 mb-2">
                Want to become a mentor?
              </h3>
              <p className="text-sm text-accent-700 mb-3">
                Share your NST experience and earn money by helping aspiring students
              </p>
              <Link
                href="/mentors/apply"
                className="inline-flex items-center text-sm font-medium text-accent-600 hover:text-accent-500"
              >
                Apply to be a mentor →
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export async function getServerSideProps() {
  const providers = await getProviders()
  return {
    props: { providers },
  }
}
