# NSTalk - "Talk to those who've lived it"

A platform connecting aspiring students with current NST students for personalized guidance through affordable 15-minute sessions.

## 🚀 Features

### For Students
- Browse NST student mentors with advanced filtering
- Book 15-minute sessions for just ₹99
- Secure payment processing via Razorpay
- Pre-session question submission
- Video call integration with Google Meet
- Rate and review mentors after sessions

### For Mentors
- Create detailed mentor profiles
- Set availability and manage bookings
- Earn money for sharing knowledge
- Track earnings and session history
- Receive student questions before sessions

### Platform Features
- Google OAuth authentication
- Responsive design with modern UI
- Admin panel for mentor approvals
- Email notifications and reminders
- Review and rating system
- Real-time dashboard analytics

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Database**: MongoDB with Mongoose
- **Authentication**: NextAuth.js with Google OAuth
- **Payments**: Razorpay integration
- **Video Calls**: Google Meet link generation
- **Styling**: Tailwind CSS with custom design system
- **Icons**: Lucide React
- **Notifications**: React Hot Toast

## 🎨 Design System

- **Primary Colors**: Navy Blue (#1E2A3A) for trust and professionalism
- **Accent Colors**: Orange (#F97316) for energy and action
- **Typography**: Inter for body text, Poppins for headings
- **Components**: Custom button styles, cards, and form elements

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd NSTalk
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in the required environment variables:
   - `MONGODB_URI`: Your MongoDB connection string
   - `NEXTAUTH_SECRET`: Random secret for NextAuth
   - `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET`: Google OAuth credentials
   - `RAZORPAY_KEY_ID` & `RAZORPAY_KEY_SECRET`: Razorpay API keys

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`

### Razorpay Setup
1. Sign up at [Razorpay](https://razorpay.com/)
2. Get your API keys from the dashboard
3. Configure webhook endpoints for payment verification

### MongoDB Setup
1. Create a MongoDB Atlas account or use local MongoDB
2. Create a new database named `nstalk`
3. Get the connection string and add to environment variables

## 📁 Project Structure

```
NSTalk/
├── components/          # Reusable React components
│   └── Layout.js       # Main layout wrapper
├── lib/                # Utility functions and configurations
│   └── mongodb.js      # Database connection
├── models/             # Mongoose schemas
│   ├── User.js         # User/Mentor model
│   ├── Session.js      # Booking session model
│   └── Review.js       # Review and rating model
├── pages/              # Next.js pages and API routes
│   ├── api/            # API endpoints
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # User dashboards
│   ├── mentors/        # Mentor-related pages
│   ├── _app.js         # App configuration
│   └── index.js        # Landing page
├── public/             # Static assets
├── styles/             # CSS and styling
│   └── globals.css     # Global styles with Tailwind
└── README.md
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

### Other Platforms
- **Netlify**: Configure build settings for Next.js
- **Railway**: Use their Next.js template
- **Heroku**: Add Node.js buildpack

## 🔐 Environment Variables

```env
# Database
MONGODB_URI=mongodb://localhost:27017/nstalk

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Razorpay
RAZORPAY_KEY_ID=your-razorpay-key-id
RAZORPAY_KEY_SECRET=your-razorpay-key-secret
```

## 🧪 Testing

```bash
# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint
```

## 📈 Future Enhancements

- [ ] Mobile app development (React Native)
- [ ] Advanced scheduling with calendar integration
- [ ] Group session capabilities
- [ ] AI-powered mentor matching
- [ ] Video recording for session reviews
- [ ] Multi-language support
- [ ] Advanced analytics dashboard
- [ ] Referral program
- [ ] Subscription plans for frequent users

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support, email <EMAIL> or join our Discord community.

## 🙏 Acknowledgments

- NST students and faculty for inspiration
- Open source community for amazing tools
- Beta testers for valuable feedback

---

**NSTalk** - Connecting dreams with experience, one conversation at a time. 🎓✨
